class_name WaterSystem
extends Node

@export var tile_query_system: TileQuerySystem
@export var player_service: PlayerService

var _player: CharacterBody2D
var _paint_component: PaintComponent
var _movement_component: MovementComponent
var _hitbox: Area2D

var _is_underwater: bool = false
var _saved_collision_layer: int = 0
var _saved_monitorable: bool = true

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_player = player_service.get_player_node()
	_paint_component = player_service.get_paint_component()
	_movement_component = player_service.get_movement_component()
	_hitbox = player_service.get_hitbox()

	var water_tiles: Array[WaterTileComponent] = tile_query_system.get_water_tiles()
	for tile in water_tiles:
		tile.player_entered.connect(_on_player_entered_water)
		tile.player_exited.connect(_on_player_exited_water)

func _on_player_entered_water(_player_body: CharacterBody2D) -> void:
	_is_underwater = true
	_saved_collision_layer = _player.collision_layer
	_player.collision_layer = 0
	_saved_monitorable = _hitbox.monitorable
	_hitbox.set_deferred("monitorable", false)
	var tile_component: WaterTileComponent = _current_water_tile()
	if is_instance_valid(tile_component) and _paint_component.current_paint > 0:
		_paint_component.current_paint -= tile_component.data.paint_cost

func _on_player_exited_water(_player_body: CharacterBody2D) -> void:
	_is_underwater = false
	_player.collision_layer = _saved_collision_layer
	_hitbox.set_deferred("monitorable", _saved_monitorable)

func _current_water_tile() -> WaterTileComponent:
	var tile: Node2D = tile_query_system.get_tile_at_global_pos(_player.global_position)
	if not is_instance_valid(tile) or tile is not WaterTileComponent:
		return null
	return tile
