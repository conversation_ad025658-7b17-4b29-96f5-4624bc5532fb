[gd_scene load_steps=13 format=3 uid="uid://dq0bjiqkyl4wv"]

[ext_resource type="Texture2D" uid="uid://cj35vjs6j4qr3" path="res://assets/banana.png" id="1_0fpyy"]
[ext_resource type="Script" uid="uid://bwkg6ydcldanv" path="res://src/player/player.gd" id="1_a7o36"]
[ext_resource type="PackedScene" uid="uid://b2minqce3p1bg" path="res://src/health/health_component.tscn" id="4_xi0yy"]
[ext_resource type="PackedScene" path="res://src/paint/paint_component.tscn" id="5_paint_component"]
[ext_resource type="PackedScene" uid="uid://d0ntxy5trg0re" path="res://src/ability/ability_component.tscn" id="6_ability_component"]
[ext_resource type="Script" uid="uid://bj3o10evva2p5" path="res://src/paint/paint_trigger.gd" id="10_8ydov"]
[ext_resource type="PackedScene" uid="uid://k2v1icx8llrc" path="res://src/movement/movement_component.tscn" id="12_aco0u"]
[ext_resource type="PackedScene" uid="uid://bncyxtrrca85p" path="res://src/player/input_component.tscn" id="14_77kuh"]
[ext_resource type="PackedScene" uid="uid://y6tbdx4l0wxy" path="res://src/player/stats_component.tscn" id="16_stats_component"]
[ext_resource type="PackedScene" path="res://src/artifact/artifact_inventory_component.tscn" id="18_artifact_inventory"]

[sub_resource type="CircleShape2D" id="CircleShape2D_0fpyy"]
radius = 3.0

[sub_resource type="CircleShape2D" id="CircleShape2D_l271a"]
radius = 1.0

[node name="Player" type="CharacterBody2D" node_paths=PackedStringArray("paint_component") groups=["player"]]
motion_mode = 1
script = ExtResource("1_a7o36")
paint_component = NodePath("PaintComponent")

[node name="StatsComponent" parent="." instance=ExtResource("16_stats_component")]

[node name="HealthComponent" parent="." instance=ExtResource("4_xi0yy")]

[node name="PaintComponent" parent="." instance=ExtResource("5_paint_component")]

[node name="AbilityComponent" parent="." instance=ExtResource("6_ability_component")]

[node name="ArtifactInventoryComponent" parent="." node_paths=PackedStringArray("ability_component") instance=ExtResource("18_artifact_inventory")]
ability_component = NodePath("../AbilityComponent")

[node name="MovementComponent" parent="." node_paths=PackedStringArray("actor", "collision_ray") instance=ExtResource("12_aco0u")]
actor = NodePath("..")
collision_ray = NodePath("../CollisionRay")

[node name="InputComponent" parent="." instance=ExtResource("14_77kuh")]

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
position = Vector2(0, -4)
texture = ExtResource("1_0fpyy")

[node name="Collision" type="CollisionShape2D" parent="."]
position = Vector2(1, -4)
shape = SubResource("CircleShape2D_0fpyy")
debug_color = Color(0.282804, 0.501787, 0.99058, 0.666667)

[node name="Hitbox" type="Area2D" parent="."]
position = Vector2(0, -4)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Hitbox"]
position = Vector2(1, 1)
shape = SubResource("CircleShape2D_0fpyy")
debug_color = Color(0.329214, 0.61418, 0.295959, 0.5)

[node name="CollisionRay" type="RayCast2D" parent="."]
target_position = Vector2(0, 0)

[node name="PaintTriggerArea" type="Area2D" parent="."]
position = Vector2(0, -4)
script = ExtResource("10_8ydov")

[node name="CollisionShape2D" type="CollisionShape2D" parent="PaintTriggerArea"]
position = Vector2(0, 3)
shape = SubResource("CircleShape2D_l271a")
