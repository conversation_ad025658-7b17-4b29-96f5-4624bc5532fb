[gd_scene load_steps=7 format=3 uid="uid://dfy35tmjoeoht"]

[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="1_7ub1g"]
[ext_resource type="Script" uid="uid://7ypb2u0vebs0" path="res://src/water_tile/water_tile_component.gd" id="1_ds36l"]
[ext_resource type="Script" uid="uid://8gsgmtcloxcl" path="res://src/water_tile/water_tile_data.gd" id="2_5hfh0"]

[sub_resource type="Resource" id="Resource_125nn"]
script = ExtResource("2_5hfh0")
paint_cost = 1
metadata/_custom_type_script = "uid://8gsgmtcloxcl"

[sub_resource type="AtlasTexture" id="AtlasTexture_ds36l"]
atlas = ExtResource("1_7ub1g")
region = Rect2(0, 0, 8, 8)

[sub_resource type="CircleShape2D" id="CircleShape2D_5hfh0"]
radius = 2.0

[node name="WaterTile" type="Node2D"]
script = ExtResource("1_ds36l")
data = SubResource("Resource_125nn")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = SubResource("AtlasTexture_ds36l")

[node name="ColorRect" type="ColorRect" parent="."]
offset_left = -4.0
offset_top = -4.0
offset_right = 3.0
offset_bottom = 3.0
color = Color(0.223529, 0.686275, 0.941176, 1)

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("CircleShape2D_5hfh0")
