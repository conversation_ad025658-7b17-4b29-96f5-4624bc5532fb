class_name WaterTileComponent
extends Node2D

signal player_entered(player_body: CharacterBody2D)
signal player_exited(player_body: CharacterBody2D)

@export var data: WaterTileData

func _ready() -> void:
	add_to_group("water_tiles")
	add_to_group("tiles")
	var area := find_child(&"Area2D", true, false) as Area2D
	if area != null:
		area.body_entered.connect(_on_body_entered)
		area.body_exited.connect(_on_body_exited)

func _on_body_entered(body: Node) -> void:
	if body is CharacterBody2D and body.is_in_group(&"player"):
		player_entered.emit(body as CharacterBody2D)

func _on_body_exited(body: Node) -> void:
	if body is CharacterBody2D and body.is_in_group(&"player"):
		player_exited.emit(body as CharacterBody2D)
